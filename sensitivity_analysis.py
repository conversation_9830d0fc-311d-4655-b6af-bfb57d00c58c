# -*- coding: utf-8 -*-

# =============================================================================
# 完整代码：使用莫里斯方法进行全局灵敏度分析
# =============================================================================

# 1. 导入必要的库
import numpy as np
import matplotlib.pyplot as plt
from SALib.analyze import morris
from SALib.sample import morris as morris_sample
from SALib.test_functions import Ishigami

# =============================================================================
# 2. 定义问题 (Problem Definition)
#    - num_vars: 模型的输入参数数量
#    - names: 每个参数的名称列表
#    - bounds: 每个参数的取值范围列表
# =============================================================================
# 我们使用 Ishigami 函数作为示例模型。
# 它有3个输入参数 (x1, x2, x3)，每个参数的范围都是 [-π, π]。
problem = {"num_vars": 3, "names": ["x1", "x2", "x3"], "bounds": [[-np.pi, np.pi]] * 3}

# =============================================================================
# 3. 生成输入样本 (Generate Samples)
#    - N: 轨迹的数量 (number of trajectories)。值越大，探索越充分。
#    - num_levels: 参数空间离散化的水平数。通常取偶数，如4, 6, 8。
#    - optimal_trajectories: 使用优化的轨迹生成方法，可以提高抽样效率。
# =============================================================================
N = 100  # 轨迹数
num_levels = 6  # 离散水平数

# 使用 SALib 的 morris_sample.sample 函数生成输入参数矩阵
# 返回的 param_values 包含了所有需要运行模型的参数组合
# 注意：不使用 optimal_trajectories=True 来避免版本兼容性问题
param_values = morris_sample.sample(problem, N=N, num_levels=num_levels)

# 打印生成的样本矩阵形状以供参考
# 形状为 (N * (D+1), D)，其中 D 是参数数量 (num_vars)
print(f"生成的样本矩阵形状: {param_values.shape}")

# =============================================================================
# 4. 运行你的模型 (Run the Model)
#    - 在这里，你需要用你自己的模型函数来替换 `Ishigami.evaluate`
#    - 输入是 `param_values` 矩阵，输出是一个一维的 NumPy 数组 `Y`
# =============================================================================
# 使用 Ishigami 函数评估所有样本点
Y = Ishigami.evaluate(param_values)

# 打印模型输出Y的形状以供参考
print(f"模型输出Y的形状: {Y.shape}")

# =============================================================================
# 5. 分析结果 (Analyze the Results)
#    - 将问题定义、输入样本和模型输出喂给 morris.analyze 函数
#    - 它会计算出每个参数的灵敏度指标：μ* (mu_star) 和 σ (sigma)
# =============================================================================
# conf_level=0.95 表示计算95%的置信区间
Si = morris.analyze(problem, param_values, Y, conf_level=0.95, num_levels=num_levels)

# 打印计算出的灵敏度指标
print("\n--- 莫里斯分析结果 ---")
# 使用 zip 将参数名和结果打包在一起循环打印
for name, mu_star, sigma, mu_star_conf in zip(
    problem["names"], Si["mu_star"], Si["sigma"], Si["mu_star_conf"]
):
    print(f"参数 {name}:")
    print(f"  μ* (mu_star)      = {mu_star:.3f}")
    print(f"  σ (sigma)         = {sigma:.3f}")
    print(f"  μ* 置信区间      = {mu_star_conf:.3f}")


# =============================================================================
# 6. 可视化 (Visualize the Results)
#    - 将 μ* 作为横轴，σ 作为纵轴，绘制散点图 (Morris Plot)
#    - 这张图可以直观地展示每个参数的重要性和交互作用
# =============================================================================
def plot_morris_sensitivity(analysis_result, problem_names):
    """
    绘制莫里斯方法的灵敏度分析结果图。
    """
    fig, ax = plt.subplots(figsize=(10, 8))

    mu_star = analysis_result["mu_star"]
    sigma = analysis_result["sigma"]
    mu_star_conf = analysis_result["mu_star_conf"]

    # 绘制散点
    ax.scatter(
        mu_star, sigma, s=150, c="royalblue", alpha=0.8, edgecolors="black", zorder=3
    )

    # 添加误差棒 (表示μ*的置信区间)
    ax.errorbar(
        mu_star,
        sigma,
        xerr=mu_star_conf,
        fmt="o",
        color="black",
        ecolor="gray",
        elinewidth=1.5,
        capsize=4,
        zorder=2,
    )

    # 为每个点添加参数名称标签
    for i, txt in enumerate(problem_names):
        ax.annotate(
            txt, (mu_star[i] * 1.03, sigma[i] * 1.03), fontsize=14, weight="bold"
        )

    # 设置坐标轴标签和标题
    ax.set_xlabel("μ* (Overall Influence)", fontsize=16)
    ax.set_ylabel("σ (Interaction / Non-linear Effects)", fontsize=16)
    ax.set_title("Morris Method Sensitivity Analysis", fontsize=18, weight="bold")

    # 优化外观
    ax.grid(True, linestyle="--", alpha=0.5)
    ax.tick_params(axis="both", which="major", labelsize=12)
    ax.axhline(y=0, color="black", linewidth=0.5)
    ax.axvline(x=0, color="black", linewidth=0.5)

    # 设置坐标轴范围，确保从0开始
    ax.set_xlim(left=0)
    ax.set_ylim(bottom=0)

    # 添加参考线 (对角线)，用于区分主效应和交互效应
    lims = [
        min(ax.get_xlim()[0], ax.get_ylim()[0]),
        max(ax.get_xlim()[1], ax.get_ylim()[1]),
    ]
    ax.plot(lims, lims, "k--", alpha=0.75, zorder=1, label="μ* = σ (Reference Line)")

    plt.legend(fontsize=12)
    plt.tight_layout()

    # 保存图表到文件
    plt.savefig("morris_sensitivity_analysis.png", dpi=300, bbox_inches="tight")
    print("图表已保存为 'morris_sensitivity_analysis.png'")
    plt.show()


# 调用绘图函数
plot_morris_sensitivity(Si, problem["names"])
